import { IStorageProvider } from '../interfaces/IStorageProvider';
import { IStorageConfig, ICloudStorageConfig } from '../types/StorageConfig';
import { StorageType } from '../enums/StorageType';
import { 
  StorageResult, 
  ObjectMetadata, 
  GetOptions, 
  PutOptions, 
  ListOptions, 
  SignedUrlOptions,
  BatchOptions,
  StreamOptions,
  StorageStats,
  MultipartUploadOptions,
  MultipartUploadInfo,
  UploadPartInfo,
  StorageResultFactory
} from '../types/StorageResult';

// AWS SDK类型定义
interface S3Client {
  send(command: any): Promise<any>;
}

interface GetObjectCommand {
  new (params: any): any;
}

// 声明全局的AWS SDK
declare global {
  interface Window {
    AWS: any;
  }
  const AWS: any;
}

/**
 * MinIO存储提供者
 * 完全实现 IStorageProvider 接口
 */
export class MinioProvider implements IStorageProvider {
  // 基本属性
  readonly name: string;
  readonly type: StorageType = StorageType.MINIO;
  
  // 私有属性
  private s3Client: any = null;
  private config: ICloudStorageConfig | null = null;
  private bucketName: string = '';
  private _isInitialized: boolean = false;
  private baseUrl: string = '';
  private region: string = 'us-east-1';

  constructor(name: string = 'MinIO') {
    this.name = name;
  }

  // 实现 IStorageProvider 接口的 isInitialized 属性
  get isInitialized(): boolean {
    return this._isInitialized;
  }

  /**
   * 初始化MinIO存储提供者
   */
  async initialize(config: IStorageConfig): Promise<void> {
    const cloudConfig = config as ICloudStorageConfig;
    try {
      // 验证配置参数
      this.validateConfig(cloudConfig);

      this.config = cloudConfig;
      this.bucketName = cloudConfig.bucketName;

      // 构建基础URL - 支持新旧字段名称
      const endPoint = (cloudConfig as any).endPoint || cloudConfig.endpoint;
      const port = (cloudConfig as any).port || 9000;
      const useSSL = (cloudConfig as any).useSSL !== undefined ? (cloudConfig as any).useSSL : true;
      const protocol = useSSL ? 'https' : 'http';
      this.baseUrl = `${protocol}://${endPoint}:${port}`;

      // 设置区域
      this.region = (cloudConfig as any).region || 'us-east-1';

      // 检查AWS SDK是否可用
      if (typeof window !== 'undefined' && typeof (window as any).AWS !== 'undefined') {
        // 浏览器环境，使用全局的AWS SDK
        const AWS = (window as any).AWS;
        this.s3Client = new AWS.S3({
          endpoint: this.baseUrl,
          accessKeyId: cloudConfig.accessKey,
          secretAccessKey: cloudConfig.secretKey,
          s3ForcePathStyle: true, // MinIO需要路径风格
          signatureVersion: 'v4',
          region: this.region
        });
      } else if (typeof global !== 'undefined' && typeof (global as any).AWS !== 'undefined') {
        // Node.js环境，使用全局的AWS SDK
        const AWS = (global as any).AWS;
        this.s3Client = new AWS.S3({
          endpoint: this.baseUrl,
          accessKeyId: cloudConfig.accessKey,
          secretAccessKey: cloudConfig.secretKey,
          s3ForcePathStyle: true,
          signatureVersion: 'v4',
          region: this.region
        });
      } else {
        // 创建模拟客户端
        this.s3Client = this.createMockS3Client();
      }

      this._isInitialized = true;
      console.log('MinIO存储提供者初始化成功');
    } catch (error) {
      console.error('初始化MinIO存储提供者失败:', error);
      this._isInitialized = false;
      throw error;
    }
  }

  /**
   * 释放资源
   */
  async dispose(): Promise<void> {
    this.s3Client = null;
    this.config = null;
    this.bucketName = '';
    this._isInitialized = false;
    console.log('MinIO存储提供者已释放');
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    if (!this._isInitialized) {
      throw new Error('MinIO存储提供者未初始化');
    }

    try {
      // 尝试列出对象来测试连接
      await this._fetchWithAuth(`${this.baseUrl}/${this.bucketName}?max-keys=1`, {
        method: 'GET'
      });
      return true;
    } catch (error) {
      console.error('测试MinIO连接失败:', error);
      return false;
    }
  }

  /**
   * 获取对象 - 实现 IStorageProvider.get
   */
  async get(key: string, options?: GetOptions): Promise<StorageResult<any>> {
    if (!this._isInitialized) {
      return StorageResultFactory.failure(new Error('MinIO存储提供者未初始化'));
    }

    return StorageResultFactory.fromPromise(
      (async () => {
        const response = await this._fetchWithAuth(`${this.baseUrl}/${this.bucketName}/${key}`, {
          method: 'GET'
        });

        if (!response.ok) {
          throw new Error(`获取对象失败: ${response.status} ${response.statusText}`);
        }

        // 根据选项处理返回数据
        const contentType = response.headers.get('content-type');
        
        if (options?.saveByType === 'arraybuffer') {
          return await response.arrayBuffer();
        } else if (options?.saveByType === 'text') {
          return await response.text();
        } else if (contentType && contentType.includes('application/json')) {
          try {
            return await response.json();
          } catch (e) {
            // 如果解析失败，返回原始数据
            return await response.arrayBuffer();
          }
        }

        // 默认返回原始数据
        return await response.arrayBuffer();
      })()
    );
  }

  /**
   * 上传对象 - 实现 IStorageProvider.put
   */
  async put(key: string, data: any, options?: PutOptions): Promise<StorageResult<void>> {
    if (!this._isInitialized) {
      return StorageResultFactory.failure(new Error('MinIO存储提供者未初始化'));
    }

    return StorageResultFactory.fromPromise(
      (async (): Promise<void> => {
        // 准备请求头
        const headers: any = {
          'Content-Type': options?.contentType || 'application/octet-stream'
        };

        // 添加元数据
        if (options?.metadata) {
          Object.keys(options.metadata).forEach(k => {
            headers[`x-amz-meta-${k}`] = options.metadata![k];
          });
        }

        // 处理请求体
        let processedBody = data;

        // 处理不同类型的输入数据
        if (data instanceof ArrayBuffer) {
          processedBody = new Uint8Array(data);
        } else if (data instanceof Uint8Array) {
          // Uint8Array保持不变
        } else if (typeof data === 'object' && data !== null && !(data instanceof Blob) && !(data instanceof File)) {
          // 如果是JSON对象，转换为字符串
          processedBody = JSON.stringify(data);
        } else if (typeof data === 'string') {
          // 字符串数据处理
          const contentType = options?.contentType || 'application/octet-stream';
          if (contentType.startsWith('image/') || contentType.startsWith('video/') || contentType === 'application/octet-stream') {
            // 检查是否是二进制字符串
            if (data.startsWith('\x89PNG') || data.startsWith('\xff\xd8\xff') || data.startsWith('GIF')) {
              // 这是二进制数据被错误地解释为字符串
              try {
                const bytes = new Uint8Array(data.length);
                for (let i = 0; i < data.length; i++) {
                  bytes[i] = data.charCodeAt(i) & 0xff;
                }
                processedBody = bytes;
              } catch (error) {
                // 如果转换失败，尝试创建Blob
                processedBody = new Blob([data], { type: contentType });
              }
            } else {
              // 如果不是二进制字符串，但内容类型是二进制，创建Blob
              processedBody = new Blob([data], { type: contentType });
            }
          }
        }

        // 发送请求
        const response = await this._fetchWithAuth(`${this.baseUrl}/${this.bucketName}/${key}`, {
          method: 'PUT',
          headers,
          body: processedBody
        });

        if (!response.ok) {
          throw new Error(`上传对象失败: ${response.status} ${response.statusText}`);
        }
      })()
    );
  }

  /**
   * 删除对象 - 实现 IStorageProvider.delete
   */
  async delete(key: string): Promise<StorageResult<void>> {
    if (!this._isInitialized) {
      return StorageResultFactory.failure(new Error('MinIO存储提供者未初始化'));
    }

    return StorageResultFactory.fromPromise(
      (async (): Promise<void> => {
        const response = await this._fetchWithAuth(`${this.baseUrl}/${this.bucketName}/${key}`, {
          method: 'DELETE'
        });

        if (!response.ok) {
          throw new Error(`删除对象失败: ${response.status} ${response.statusText}`);
        }
      })()
    );
  }

  /**
   * 列出对象 - 实现 IStorageProvider.list
   */
  async list(prefix?: string, options?: ListOptions): Promise<StorageResult<string[]>> {
    if (!this._isInitialized) {
      return StorageResultFactory.failure(new Error('MinIO存储提供者未初始化'));
    }

    return StorageResultFactory.fromPromise(
      (async (): Promise<string[]> => {
        // 构建查询参数
        const queryParams = new URLSearchParams();
        if (prefix) queryParams.set('prefix', prefix);
        if (options?.marker) queryParams.set('marker', options.marker);
        if (options?.maxKeys) queryParams.set('max-keys', options.maxKeys.toString());
        if (options?.delimiter) queryParams.set('delimiter', options.delimiter);

        // 发送请求
        const url = `${this.baseUrl}/${this.bucketName}?${queryParams.toString()}`;
        const response = await this._fetchWithAuth(url, {
          method: 'GET'
        });

        if (!response.ok) {
          throw new Error(`列出对象失败: ${response.status} ${response.statusText}`);
        }

        // 解析XML响应
        const text = await response.text();
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(text, 'text/xml');

        // 检查是否有错误
        const errorNode = xmlDoc.querySelector('Error');
        if (errorNode) {
          const code = errorNode.querySelector('Code')?.textContent || 'UnknownError';
          const message = errorNode.querySelector('Message')?.textContent || 'Unknown error';
          throw new Error(`MinIO错误: ${code} - ${message}`);
        }

        // 提取对象键
        const contents = Array.from(xmlDoc.querySelectorAll('Contents'));
        const keys = contents.map(content => {
          return content.querySelector('Key')?.textContent || '';
        }).filter(key => key !== '');

        return keys;
      })()
    );
  }

  /**
   * 验证配置参数
   */
  private validateConfig(config: ICloudStorageConfig): void {
    if (!config.accessKey) {
      throw new Error('accessKey不能为空');
    }
    if (!config.secretKey) {
      throw new Error('secretKey不能为空');
    }
    
    const endPoint = (config as any).endPoint || config.endpoint;
    if (!endPoint) {
      throw new Error('endPoint/endpoint不能为空');
    }
    if (!config.bucketName) {
      throw new Error('bucketName不能为空');
    }
  }

  /**
   * 创建模拟S3客户端
   */
  private createMockS3Client(): any {
    return {
      getSignedUrl: null // 模拟客户端不支持预签名URL
    };
  }

  /**
   * 使用AWS签名版本4对请求进行身份验证
   */
  private async _fetchWithAuth(url: string, options: any = {}): Promise<Response> {
    // 这里需要实现AWS4签名逻辑
    // 为了简化，我们先返回一个基本的fetch请求
    // 在实际使用中，需要实现完整的AWS4签名
    
    const headers = options.headers || {};
    
    // 添加基本的认证头（这是简化版本）
    const auth = btoa(`${this.config!.accessKey}:${this.config!.secretKey}`);
    headers['Authorization'] = `Basic ${auth}`;
    
    return fetch(url, {
      ...options,
      headers
    });
  }

  // 以下是需要实现但暂时简化的方法

  async getBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<Record<string, any>>> {
    const results: Record<string, any> = {};
    for (const key of keys) {
      try {
        const result = await this.get(key);
        if (result.success) {
          results[key] = result.data;
        }
      } catch (error) {
        if (!options?.continueOnError) {
          return StorageResultFactory.failure(error as Error);
        }
      }
    }
    return StorageResultFactory.success(results);
  }

  async putBatch(items: Record<string, any>, options?: BatchOptions): Promise<StorageResult<void>> {
    for (const [key, data] of Object.entries(items)) {
      try {
        const result = await this.put(key, data);
        if (!result.success) {
          throw result.error;
        }
      } catch (error) {
        if (!options?.continueOnError) {
          return StorageResultFactory.failure(error as Error);
        }
      }
    }
    return StorageResultFactory.success();
  }

  async deleteBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<void>> {
    for (const key of keys) {
      try {
        const result = await this.delete(key);
        if (!result.success) {
          throw result.error;
        }
      } catch (error) {
        if (!options?.continueOnError) {
          return StorageResultFactory.failure(error as Error);
        }
      }
    }
    return StorageResultFactory.success();
  }

  async getMetadata(key: string): Promise<StorageResult<ObjectMetadata>> {
    if (!this._isInitialized) {
      return StorageResultFactory.failure(new Error('MinIO存储提供者未初始化'));
    }

    return StorageResultFactory.fromPromise(
      (async (): Promise<ObjectMetadata> => {
        const response = await this._fetchWithAuth(`${this.baseUrl}/${this.bucketName}/${key}`, {
          method: 'HEAD'
        });

        if (!response.ok) {
          throw new Error(`获取对象元数据失败: ${response.status} ${response.statusText}`);
        }

        const metadata: ObjectMetadata = {
          etag: (response.headers.get('etag') || response.headers.get('ETag') || '').replace(/"/g, ''),
          size: parseInt(response.headers.get('content-length') || '0', 10),
          lastModified: new Date(response.headers.get('last-modified') || ''),
          contentType: response.headers.get('content-type') || 'application/octet-stream'
        };

        return metadata;
      })()
    );
  }

  async getStream(key: string, options?: StreamOptions): Promise<ReadableStream> {
    throw new Error('MinIO暂不支持流式读取，请使用get方法');
  }

  async putStream(key: string, stream: ReadableStream, options?: PutOptions & StreamOptions): Promise<StorageResult<void>> {
    throw new Error('MinIO暂不支持流式写入，请使用put方法');
  }

  async initiateMultipartUpload(key: string, options?: MultipartUploadOptions): Promise<StorageResult<string>> {
    return StorageResultFactory.failure(new Error('分块上传功能待实现'));
  }

  async uploadPart(key: string, uploadId: string, partNumber: number, data: Uint8Array): Promise<StorageResult<UploadPartInfo>> {
    return StorageResultFactory.failure(new Error('分块上传功能待实现'));
  }

  async completeMultipartUpload(key: string, uploadId: string, parts: UploadPartInfo[]): Promise<StorageResult<void>> {
    return StorageResultFactory.failure(new Error('分块上传功能待实现'));
  }

  async abortMultipartUpload(key: string, uploadId: string): Promise<StorageResult<void>> {
    return StorageResultFactory.failure(new Error('分块上传功能待实现'));
  }

  async listMultipartUploads(prefix?: string): Promise<StorageResult<MultipartUploadInfo[]>> {
    return StorageResultFactory.success([]);
  }

  async getSignedUrl(key: string, options?: SignedUrlOptions): Promise<string> {
    if (!this._isInitialized) {
      throw new Error('MinIO存储提供者未初始化');
    }

    try {
      const expires = options?.expires || 3600;
      const method = options?.method || 'GET';

      // 使用AWS SDK的getSignedUrl方法
      if (this.s3Client && this.s3Client.getSignedUrl) {
        const params = {
          Bucket: this.bucketName,
          Key: key,
          Expires: expires
        };

        return new Promise((resolve, reject) => {
          this.s3Client!.getSignedUrl(method.toLowerCase(), params, (err: any, url: string) => {
            if (err) {
              reject(err);
            } else {
              resolve(url);
            }
          });
        });
      } else {
        // 模拟实现
        const minioConfig = this.config as ICloudStorageConfig;
        const endPoint = (minioConfig as any).endPoint || minioConfig.endpoint;
        const port = (minioConfig as any).port || 9000;
        const useSSL = (minioConfig as any).useSSL !== undefined ? (minioConfig as any).useSSL : true;
        const protocol = useSSL ? 'https' : 'http';
        const baseUrl = `${protocol}://${endPoint}:${port}`;
        
        return `${baseUrl}/${this.bucketName}/${key}?X-Amz-Expires=${expires}&X-Amz-Algorithm=AWS4-HMAC-SHA256`;
      }
    } catch (error) {
      throw new Error(`生成预签名URL失败: ${(error as Error).message}`);
    }
  }

  async getStats(): Promise<StorageResult<StorageStats>> {
    return StorageResultFactory.success({
      totalSize: 0,
      objectCount: 0,
      lastModified: new Date()
    });
  }

  getConfig(): IStorageConfig {
    if (!this.config) {
      throw new Error('配置未初始化');
    }
    return this.config;
  }

  async updateConfig(config: Partial<IStorageConfig>): Promise<void> {
    if (!this.config) {
      throw new Error('配置未初始化');
    }
    
    // 更新配置
    this.config = { ...this.config, ...config } as ICloudStorageConfig;
    
    // 如果是关键配置更改，需要重新初始化
    if (config.endpoint || (config as any).accessKey || (config as any).secretKey) {
      await this.dispose();
      await this.initialize(this.config);
    }
  }
}
