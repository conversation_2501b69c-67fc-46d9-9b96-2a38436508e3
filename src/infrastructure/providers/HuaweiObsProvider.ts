import { IStorageProvider } from '../interfaces/IStorageProvider';
import { IStorageConfig, ICloudStorageConfig } from '../types/StorageConfig';
import { StorageType } from '../enums/StorageType';
import { 
  StorageResult, 
  ObjectMetadata, 
  GetOptions, 
  PutOptions, 
  ListOptions, 
  SignedUrlOptions,
  BatchOptions,
  StreamOptions,
  StorageStats,
  MultipartUploadOptions,
  MultipartUploadInfo,
  UploadPartInfo,
  StorageResultFactory
} from '../types/StorageResult';

// 华为云OBS SDK类型定义
interface ObsClient {
  headBucket(params: any, callback?: Function): any;
  getObject(params: any, callback?: Function): any;
  putObject(params: any, callback?: Function): any;
  deleteObject(params: any, callback?: Function): any;
  deleteObjects(params: any, callback?: Function): any;
  listObjects(params: any, callback?: Function): any;
  headObject(params: any, callback?: Function): any;
  createSignedUrl(params: any, callback?: Function): any;
  createSignedUrlSync(params: any): any;
  getObjectMetadata(params: any, callback?: Function): any;
  initiateMultipartUpload(params: any, callback?: Function): any;
  uploadPart(params: any, callback?: Function): any;
  completeMultipartUpload(params: any, callback?: Function): any;
  abortMultipartUpload(params: any, callback?: Function): any;
  listMultipartUploads(params: any, callback?: Function): any;
}

// 声明全局的ObsClient构造函数
declare global {
  interface Window {
    ObsClient: new (config: any) => ObsClient;
  }
  const ObsClient: new (config: any) => ObsClient;
}

/**
 * 华为云OBS存储提供者
 * 完全实现 IStorageProvider 接口
 */
export class HuaweiObsProvider implements IStorageProvider {
  // 基本属性
  readonly name: string;
  readonly type: StorageType = StorageType.HUAWEI_OBS;
  
  // 私有属性
  private obsClient: ObsClient | null = null;
  private config: ICloudStorageConfig | null = null;
  private bucketName: string = '';
  private _isInitialized: boolean = false;
  private baseUrl: string = '';

  constructor(name: string = 'HuaweiOBS') {
    this.name = name;
  }

  // 实现 IStorageProvider 接口的 isInitialized 属性
  get isInitialized(): boolean {
    return this._isInitialized;
  }

  /**
   * 初始化华为云OBS存储提供者
   */
  async initialize(config: IStorageConfig): Promise<void> {
    const cloudConfig = config as ICloudStorageConfig;
    try {
      // 验证配置参数
      this.validateConfig(cloudConfig);

      this.config = cloudConfig;

      // 获取ObsClient构造函数
      const ObsClientConstructor = typeof window !== 'undefined' && window.ObsClient 
        ? window.ObsClient 
        : (typeof global !== 'undefined' && (global as any).ObsClient)
          ? (global as any).ObsClient
          : null;

      if (!ObsClientConstructor) {
        throw new Error('华为云OBS SDK未加载，请确保已正确引入esdk-obs-browserjs');
      }

      // 支持新旧字段名称
      const accessKeyId = (cloudConfig as any).accessKeyId || cloudConfig.accessKey;
      const secretAccessKey = (cloudConfig as any).secretAccessKey || cloudConfig.secretKey;

      // 创建OBS客户端
      this.obsClient = new ObsClientConstructor({
        access_key_id: accessKeyId,
        secret_access_key: secretAccessKey,
        server: `https://${cloudConfig.endpoint}`,
        timeout: cloudConfig.timeout || 60000,
        max_retry_count: cloudConfig.retryCount || 3,
      });

      this.bucketName = cloudConfig.bucketName;
      this.baseUrl = `https://${this.bucketName}.${cloudConfig.endpoint}`;
      this._isInitialized = true;

      console.log('华为云OBS存储提供者初始化成功');
    } catch (error) {
      console.error('初始化华为云OBS存储提供者失败:', error);
      this._isInitialized = false;
      throw error;
    }
  }

  /**
   * 释放资源
   */
  async dispose(): Promise<void> {
    this.obsClient = null;
    this.config = null;
    this.bucketName = '';
    this._isInitialized = false;
    console.log('华为云OBS存储提供者已释放');
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    if (!this._isInitialized || !this.obsClient) {
      throw new Error('OBS存储提供者未初始化');
    }

    return new Promise((resolve, reject) => {
      this.obsClient!.headBucket({
        Bucket: this.bucketName
      }, (err: any, result: any) => {
        if (err) {
          reject(err);
        } else if (result.CommonMsg.Status < 300) {
          resolve(true);
        } else {
          reject(new Error(`存储桶不存在或无权限访问。状态码：${result.CommonMsg.Status}`));
        }
      });
    });
  }

  /**
   * 获取对象 - 实现 IStorageProvider.get
   */
  async get(key: string, options?: GetOptions): Promise<StorageResult<any>> {
    if (!this._isInitialized || !this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS存储提供者未初始化'));
    }

    return StorageResultFactory.fromPromise(
      new Promise((resolve, reject) => {
        this.obsClient!.getObject({
          Bucket: this.bucketName,
          Key: key
        }, (err: any, result: any) => {
          if (err) {
            console.error(`获取对象${key}失败:`, err);
            reject(err);
            return;
          }

          if (!result) {
            reject(new Error(`获取对象${key}失败: 没有返回结果`));
            return;
          }

          if (result.CommonMsg.Status < 300) {
            if (!result.InterfaceResult || !result.InterfaceResult.Content) {
              reject(new Error(`获取对象${key}失败: 没有内容`));
              return;
            }

            try {
              // 根据选项处理返回数据
              const content = result.InterfaceResult.Content;
              if (options?.saveByType === 'arraybuffer') {
                // 华为云OBS特有的arraybuffer支持
                resolve(content);
              } else if (options?.saveByType === 'text') {
                resolve(typeof content === 'string' ? content : JSON.stringify(content));
              } else {
                // 默认尝试解析JSON
                resolve(typeof content === 'string' ? JSON.parse(content) : content);
              }
            } catch (parseError) {
              // 如果不是JSON，返回原始内容
              resolve(result.InterfaceResult.Content);
            }
          } else {
            reject(new Error(`获取对象${key}失败，状态码: ${result.CommonMsg.Status}`));
          }
        });
      })
    );
  }

  /**
   * 上传对象 - 实现 IStorageProvider.put
   */
  async put(key: string, data: any, options?: PutOptions): Promise<StorageResult<void>> {
    if (!this._isInitialized || !this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS存储提供者未初始化'));
    }

    return StorageResultFactory.fromPromise(
      new Promise<void>((resolve, reject) => {
        const params: any = {
          Bucket: this.bucketName,
          Key: key,
          Body: data
        };

        // 添加可选参数
        if (options?.contentType) {
          params.ContentType = options.contentType;
        }

        if (options?.metadata) {
          params.Metadata = options.metadata;
        }

        this.obsClient!.putObject(params, (err: any, result: any) => {
          if (err) {
            console.error(`上传对象${key}失败:`, err);
            reject(err);
          } else if (result.CommonMsg.Status < 300) {
            resolve(); // 成功时不返回数据，符合 StorageResult<void>
          } else {
            const error = new Error(`上传对象${key}失败，状态码: ${result.CommonMsg.Status}`);
            console.error(error);
            reject(error);
          }
        });
      })
    );
  }

  /**
   * 删除对象 - 实现 IStorageProvider.delete
   */
  async delete(key: string): Promise<StorageResult<void>> {
    if (!this._isInitialized || !this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS存储提供者未初始化'));
    }

    return StorageResultFactory.fromPromise(
      new Promise<void>((resolve, reject) => {
        this.obsClient!.deleteObject({
          Bucket: this.bucketName,
          Key: key
        }, (err: any, result: any) => {
          if (err) {
            console.error(`删除对象${key}失败:`, err);
            reject(err);
          } else if (result.CommonMsg.Status < 300) {
            resolve();
          } else {
            const error = new Error(`删除对象${key}失败，状态码: ${result.CommonMsg.Status}`);
            console.error(error);
            reject(error);
          }
        });
      })
    );
  }

  /**
   * 列出对象 - 实现 IStorageProvider.list
   */
  async list(prefix?: string, options?: ListOptions): Promise<StorageResult<string[]>> {
    if (!this._isInitialized || !this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS存储提供者未初始化'));
    }

    return StorageResultFactory.fromPromise(
      new Promise<string[]>((resolve, reject) => {
        const params: any = {
          Bucket: this.bucketName
        };

        // 添加可选参数
        if (prefix) {
          params.Prefix = prefix;
        }

        if (options?.marker) {
          params.Marker = options.marker;
        }

        if (options?.maxKeys) {
          params.MaxKeys = options.maxKeys;
        }

        if (options?.delimiter) {
          params.Delimiter = options.delimiter;
        }

        this.obsClient!.listObjects(params, (err: any, result: any) => {
          if (err) {
            console.error('列出对象失败:', err);
            reject(err);
          } else if (result.CommonMsg.Status < 300) {
            const keys = (result.InterfaceResult.Contents || []).map((item: any) => item.Key);
            resolve(keys);
          } else {
            const error = new Error(`列出对象失败，状态码: ${result.CommonMsg.Status}`);
            console.error(error);
            reject(error);
          }
        });
      })
    );
  }

  /**
   * 验证配置参数
   */
  private validateConfig(config: ICloudStorageConfig): void {
    const accessKey = (config as any).accessKeyId || config.accessKey;
    const secretKey = (config as any).secretAccessKey || config.secretKey;
    
    if (!accessKey) {
      throw new Error('accessKeyId/accessKey不能为空');
    }
    if (!secretKey) {
      throw new Error('secretAccessKey/secretKey不能为空');
    }
    if (!config.endpoint) {
      throw new Error('endpoint不能为空');
    }
    if (!config.bucketName) {
      throw new Error('bucketName不能为空');
    }
  }

  // 以下是需要实现但暂时简化的方法

  async getBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<Record<string, any>>> {
    // 批量获取的简化实现
    const results: Record<string, any> = {};
    for (const key of keys) {
      try {
        const result = await this.get(key);
        if (result.success) {
          results[key] = result.data;
        }
      } catch (error) {
        if (!options?.continueOnError) {
          return StorageResultFactory.failure(error as Error);
        }
      }
    }
    return StorageResultFactory.success(results);
  }

  async putBatch(items: Record<string, any>, options?: BatchOptions): Promise<StorageResult<void>> {
    // 批量上传的简化实现
    for (const [key, data] of Object.entries(items)) {
      try {
        const result = await this.put(key, data);
        if (!result.success) {
          throw result.error;
        }
      } catch (error) {
        if (!options?.continueOnError) {
          return StorageResultFactory.failure(error as Error);
        }
      }
    }
    return StorageResultFactory.success();
  }

  async deleteBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<void>> {
    // 批量删除的简化实现
    for (const key of keys) {
      try {
        const result = await this.delete(key);
        if (!result.success) {
          throw result.error;
        }
      } catch (error) {
        if (!options?.continueOnError) {
          return StorageResultFactory.failure(error as Error);
        }
      }
    }
    return StorageResultFactory.success();
  }

  async getMetadata(key: string): Promise<StorageResult<ObjectMetadata>> {
    if (!this._isInitialized || !this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS存储提供者未初始化'));
    }

    return StorageResultFactory.fromPromise(
      new Promise<ObjectMetadata>((resolve, reject) => {
        this.obsClient!.getObjectMetadata({
          Bucket: this.bucketName,
          Key: key
        }, (err: any, result: any) => {
          if (err) {
            reject(err);
          } else if (result.CommonMsg.Status < 300) {
            const metadata: ObjectMetadata = {
              etag: result.InterfaceResult.ETag,
              size: result.InterfaceResult.ContentLength,
              lastModified: new Date(result.InterfaceResult.LastModified),
              contentType: result.InterfaceResult.ContentType
            };
            resolve(metadata);
          } else {
            reject(new Error(`获取对象元数据失败，状态码: ${result.CommonMsg.Status}`));
          }
        });
      })
    );
  }

  async getStream(key: string, options?: StreamOptions): Promise<ReadableStream> {
    throw new Error('华为云OBS暂不支持流式读取，请使用get方法');
  }

  async putStream(key: string, stream: ReadableStream, options?: PutOptions & StreamOptions): Promise<StorageResult<void>> {
    throw new Error('华为云OBS暂不支持流式写入，请使用put方法');
  }

  async initiateMultipartUpload(key: string, options?: MultipartUploadOptions): Promise<StorageResult<string>> {
    // 分块上传初始化的简化实现
    return StorageResultFactory.failure(new Error('分块上传功能待实现'));
  }

  async uploadPart(key: string, uploadId: string, partNumber: number, data: Uint8Array): Promise<StorageResult<UploadPartInfo>> {
    // 上传分块的简化实现
    return StorageResultFactory.failure(new Error('分块上传功能待实现'));
  }

  async completeMultipartUpload(key: string, uploadId: string, parts: UploadPartInfo[]): Promise<StorageResult<void>> {
    // 完成分块上传的简化实现
    return StorageResultFactory.failure(new Error('分块上传功能待实现'));
  }

  async abortMultipartUpload(key: string, uploadId: string): Promise<StorageResult<void>> {
    // 取消分块上传的简化实现
    return StorageResultFactory.failure(new Error('分块上传功能待实现'));
  }

  async listMultipartUploads(prefix?: string): Promise<StorageResult<MultipartUploadInfo[]>> {
    // 列出分块上传的简化实现
    return StorageResultFactory.success([]);
  }

  async getSignedUrl(key: string, options?: SignedUrlOptions): Promise<string> {
    if (!this._isInitialized || !this.obsClient) {
      throw new Error('OBS存储提供者未初始化');
    }

    const expires = options?.expires || 3600;
    const method = options?.method || 'GET';

    return new Promise((resolve, reject) => {
      this.obsClient!.createSignedUrl({
        Method: method.toUpperCase(),
        Bucket: this.bucketName,
        Key: key,
        Expires: expires
      }, (err: any, result: any) => {
        if (err) {
          console.error(`获取对象URL失败:`, err);
          reject(err);
        } else if (result.CommonMsg.Status < 300) {
          resolve(result.InterfaceResult.SignedUrl);
        } else {
          const error = new Error(`获取对象URL失败，状态码: ${result.CommonMsg.Status}`);
          console.error(error);
          reject(error);
        }
      });
    });
  }

  async getStats(): Promise<StorageResult<StorageStats>> {
    // 统计信息的简化实现
    return StorageResultFactory.success({
      totalSize: 0,
      objectCount: 0,
      lastModified: new Date()
    });
  }

  getConfig(): IStorageConfig {
    if (!this.config) {
      throw new Error('配置未初始化');
    }
    return this.config;
  }

  async updateConfig(config: Partial<IStorageConfig>): Promise<void> {
    if (!this.config) {
      throw new Error('配置未初始化');
    }
    
    // 更新配置
    this.config = { ...this.config, ...config } as ICloudStorageConfig;
    
    // 如果是关键配置更改，需要重新初始化
    if (config.endpoint || (config as any).accessKey || (config as any).secretKey) {
      await this.dispose();
      await this.initialize(this.config);
    }
  }
}
